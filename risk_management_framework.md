# Risk Management Framework for Small Account Cascade Trading

## 1. Risk Management Hierarchy

### 1.1 Primary Risk Controls
1. **Account-Level Risk**: Maximum total account exposure
2. **Position-Level Risk**: Individual trade risk limits
3. **Correlation Risk**: Exposure to related instruments
4. **Time-Based Risk**: Intraday and session limits
5. **Drawdown Risk**: Maximum acceptable losses

### 1.2 Risk Priority Matrix
```
Priority 1: Capital Preservation (Account survival)
Priority 2: Consistent Returns (Steady growth)
Priority 3: Profit Maximization (Optimal returns)
```

## 2. Account Size-Specific Risk Parameters

### 2.1 Micro Accounts ($30 - $100)
```
Max_Risk_Per_Trade = 0.5%
Max_Daily_Risk = 2.0%
Max_Weekly_Risk = 5.0%
Max_Monthly_Risk = 15.0%
Max_Concurrent_Positions = 2
Min_Risk_Reward_Ratio = 1:2
```

### 2.2 Small Accounts ($100 - $500)
```
Max_Risk_Per_Trade = 1.0%
Max_Daily_Risk = 3.0%
Max_Weekly_Risk = 7.0%
Max_Monthly_Risk = 18.0%
Max_Concurrent_Positions = 3
Min_Risk_Reward_Ratio = 1:1.5
```

### 2.3 Mini Accounts ($500 - $1000)
```
Max_Risk_Per_Trade = 1.5%
Max_Daily_Risk = 4.0%
Max_Weekly_Risk = 10.0%
Max_Monthly_Risk = 20.0%
Max_Concurrent_Positions = 4
Min_Risk_Reward_Ratio = 1:1.2
```

## 3. Dynamic Risk Adjustment System

### 3.1 Performance-Based Risk Scaling
```
IF consecutive_wins >= 3:
    Risk_Multiplier = 1.2 (increase risk by 20%)
    Max_Risk_Multiplier = 1.5

IF consecutive_losses >= 2:
    Risk_Multiplier = 0.8 (decrease risk by 20%)
    Min_Risk_Multiplier = 0.5

IF monthly_return > 10%:
    Risk_Multiplier = 1.1
    
IF monthly_return < -5%:
    Risk_Multiplier = 0.7
```

### 3.2 Volatility-Adjusted Risk
```
Market_Volatility = VIX_Current / VIX_Average_20
Volatility_Risk_Adjustment = 1 / sqrt(Market_Volatility)

Adjusted_Position_Size = Base_Position_Size × Volatility_Risk_Adjustment
```

### 3.3 Time-Decay Risk Management
```
Session_Progress = (Current_Time - Session_Start) / Session_Duration
Risk_Reduction_Factor = 1 - (Session_Progress × 0.3)

Late_Session_Risk = Base_Risk × Risk_Reduction_Factor
```

## 4. Position Correlation Management

### 4.1 Correlation Matrix Calculation
```
Correlation_Coefficient = Covariance(Asset_A, Asset_B) / (StdDev_A × StdDev_B)
Correlation_Threshold_High = 0.7
Correlation_Threshold_Medium = 0.5
Correlation_Threshold_Low = 0.3
```

### 4.2 Correlation-Based Position Limits
```
IF Correlation > 0.7:
    Max_Combined_Risk = 1.5% (instead of 2% for two 1% positions)
    
IF Correlation > 0.5:
    Max_Combined_Risk = 1.8%
    
IF Correlation < 0.3:
    Max_Combined_Risk = 2.0% (full allocation allowed)
```

### 4.3 Diversification Score
```
Diversification_Score = 1 - (Sum_of_Squared_Weights / Number_of_Positions)
Target_Diversification_Score = 0.8
Min_Diversification_Score = 0.6
```

## 5. Stop-Loss Management System

### 5.1 Initial Stop-Loss Calculation
```
Technical_Stop = Entry_Price ± (ATR × Stop_Multiplier)
Risk_Based_Stop = Entry_Price ± (Account_Risk / Position_Size)
Final_Stop = min(Technical_Stop, Risk_Based_Stop)
```

### 5.2 Trailing Stop Algorithms

#### 5.2.1 ATR-Based Trailing Stop
```
Trailing_Distance = ATR_14 × Trailing_Multiplier
Trailing_Multiplier = 2.0 (trending markets)
Trailing_Multiplier = 1.5 (ranging markets)

New_Stop = max(Previous_Stop, Current_Price - Trailing_Distance)
```

#### 5.2.2 Percentage-Based Trailing Stop
```
Trailing_Percentage = 3% (for volatile assets)
Trailing_Percentage = 2% (for stable assets)
Trailing_Percentage = 1.5% (for low volatility periods)

New_Stop = Current_Price × (1 - Trailing_Percentage)
```

#### 5.2.3 Chandelier Exit Trailing Stop
```
Chandelier_Period = 22
Chandelier_Multiplier = 3.0

Chandelier_Stop = Highest_High_22 - (ATR_22 × Chandelier_Multiplier)
```

## 6. Drawdown Management Protocol

### 6.1 Drawdown Calculation
```
Peak_Balance = max(Historical_Account_Balance)
Current_Drawdown = (Peak_Balance - Current_Balance) / Peak_Balance
Max_Acceptable_Drawdown = 0.20 (20%)
```

### 6.2 Drawdown Response Actions

#### 6.2.1 Level 1: 5% Drawdown
```
Actions:
- Review recent trades for pattern analysis
- Reduce position sizes by 10%
- Increase stop-loss tightness by 10%
- Limit new positions to highest probability setups
```

#### 6.2.2 Level 2: 10% Drawdown
```
Actions:
- Reduce position sizes by 25%
- Halt trading for 24 hours for strategy review
- Implement stricter entry criteria
- Consider reducing leverage
```

#### 6.2.3 Level 3: 15% Drawdown
```
Actions:
- Reduce position sizes by 50%
- Halt trading for 48 hours
- Complete strategy review and optimization
- Seek external strategy validation
```

#### 6.2.4 Level 4: 20% Drawdown
```
Actions:
- Halt all trading immediately
- Complete account and strategy audit
- Consider strategy replacement
- Implement recovery plan with reduced risk
```

## 7. Risk Monitoring and Alerts

### 7.1 Real-Time Risk Metrics
```
Current_Risk_Exposure = Sum(Open_Position_Risk)
Available_Risk_Capacity = Max_Daily_Risk - Current_Risk_Exposure
Risk_Utilization_Percentage = Current_Risk_Exposure / Max_Daily_Risk
```

### 7.2 Alert Thresholds
```
Yellow_Alert = Risk_Utilization > 60%
Orange_Alert = Risk_Utilization > 80%
Red_Alert = Risk_Utilization > 95%
Critical_Alert = Daily_Loss > Max_Daily_Risk
```

### 7.3 Automated Risk Actions
```
IF Yellow_Alert:
    - Send notification
    - Tighten new position criteria
    
IF Orange_Alert:
    - Reduce new position sizes by 25%
    - Increase monitoring frequency
    
IF Red_Alert:
    - Halt new positions
    - Consider closing weakest positions
    
IF Critical_Alert:
    - Close all positions immediately
    - Halt trading for remainder of session
```

## 8. Emergency Procedures

### 8.1 Market Crisis Response
```
Crisis_Indicators:
- VIX > 40
- Market gap > 3%
- Major news events
- System failures

Crisis_Actions:
- Reduce all positions by 50%
- Tighten stops to 1.0 × ATR
- Halt new entries
- Increase monitoring to real-time
```

### 8.2 Account Recovery Protocol
```
Recovery_Phase_1 (After significant loss):
- Risk per trade = 0.25%
- Only trade highest probability setups
- Focus on capital preservation
- Target 1:3 risk-reward minimum

Recovery_Phase_2 (After 10 consecutive profitable trades):
- Risk per trade = 0.5%
- Gradually increase position frequency
- Maintain strict discipline

Recovery_Phase_3 (After account recovery to 90% of peak):
- Return to normal risk parameters
- Resume full strategy implementation
```

## 9. Performance-Based Risk Adjustments

### 9.1 Win Rate Adjustments
```
IF Win_Rate > 70% (over 20 trades):
    - Increase position sizes by 10%
    - Consider wider stops for better entries
    
IF Win_Rate < 45% (over 20 trades):
    - Decrease position sizes by 20%
    - Tighten entry criteria
    - Review strategy effectiveness
```

### 9.2 Profit Factor Adjustments
```
IF Profit_Factor > 2.0:
    - Increase risk per trade by 15%
    - Consider adding position frequency
    
IF Profit_Factor < 1.2:
    - Decrease risk per trade by 25%
    - Focus on trade quality over quantity
```

## 10. Risk Documentation and Review

### 10.1 Daily Risk Report
```
Daily_Risk_Metrics:
- Maximum risk exposure reached
- Number of positions held
- Correlation exposure levels
- Stop-loss effectiveness
- Risk-adjusted returns
```

### 10.2 Weekly Risk Review
```
Weekly_Analysis:
- Risk parameter effectiveness
- Drawdown analysis
- Correlation matrix updates
- Stop-loss optimization
- Emergency procedure testing
```

### 10.3 Monthly Risk Audit
```
Monthly_Evaluation:
- Complete risk framework review
- Parameter optimization
- Strategy stress testing
- Risk model validation
- Performance attribution analysis
```

## Conclusion

This risk management framework provides comprehensive protection for small trading accounts while maintaining growth potential. The multi-layered approach ensures that capital preservation remains the primary focus while allowing for systematic profit generation through controlled risk-taking.
